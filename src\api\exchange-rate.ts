
// This simulates a backend API endpoint
// In a production environment, this would be an Edge Function in Supabase
// or similar server-side solution to keep the API key secure

export const handler = async () => {
  try {
    // The API key should be stored in environment variables
    // For demonstration purposes, we're just commenting where it would go
    const API_KEY = process.env.CURRENCY_FREAKS_API_KEY || "demo"; // Use "demo" for testing
    
    const response = await fetch(
      `https://api.currencyfreaks.com/latest?apikey=${API_KEY}&symbols=SYP&base=USD`
    );
    
    if (!response.ok) {
      throw new Error(`Currency API error: ${response.status}`);
    }
    
    const data = await response.json();
    
    // Validate the response data structure before returning
    if (!data || !data.rates || !data.rates.SYP) {
      console.error("Invalid response format:", data);
      throw new Error("Invalid response format from currency API");
    }
    
    return {
      statusCode: 200,
      body: JSON.stringify(data),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  } catch (error) {
    console.error("Error fetching exchange rate:", error);
    return {
      statusCode: 500,
      body: JSON.stringify({ error: "Failed to fetch exchange rate" }),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  }
};
