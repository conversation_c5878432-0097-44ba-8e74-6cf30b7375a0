import { Property } from "@/types/property";
import { useTranslation } from "@/contexts/TranslationContext";
import { useExchangeRate } from "@/contexts/ExchangeRateContext";

interface MarketingPostProps {
  property: Property;
}

export function MarketingPost({ property }: MarketingPostProps) {
  const { t } = useTranslation();
  const { formatPrice } = useExchangeRate();

  const generateMarketingText = (): string => {
    const lines: string[] = [];
    
    // Title with status
    const statusText = property.status === 'for-sale' 
      ? t('marketingPost.forSaleText') 
      : t('marketingPost.forRentText');
    
    lines.push(`${statusText} ✨`);
    lines.push(`${property.title}`);
    lines.push('');
    
    // Description
    if (property.description) {
      lines.push(property.description);
      lines.push('');
    }
    
    // Key details
    lines.push('🔸 ' + t('marketingPost.locationText') + ': ' + property.location);
    lines.push('🔸 ' + t('marketingPost.priceText') + ': ' + formatPrice(property.price));
    lines.push('🔸 ' + t('marketingPost.sizeText') + ': ' + property.size + ' m²');
    
    // Property type
    const typeKey = `properties.${property.type}` as const;
    lines.push('🔸 ' + t('properties.type') + ': ' + t(typeKey));
    
    // Living details (if not land)
    if (property.type !== 'land') {
      if (property.distribution) {
        lines.push('🔸 ' + t('marketingPost.distributionText') + ': ' + property.distribution);
      } else if (property.bedrooms > 0) {
        lines.push('🔸 ' + t('marketingPost.bedroomsText') + ': ' + property.bedrooms);
      }
      
      if (property.bathrooms > 0) {
        lines.push('🔸 ' + t('marketingPost.bathroomsText') + ': ' + property.bathrooms);
      }
      
      if (property.floor !== null && (property.type === 'apartment' || property.type === 'commercial')) {
        lines.push('🔸 ' + t('marketingPost.floorText') + ': ' + property.floor);
      }
      
      if (property.furnished && property.furnished !== 'unfurnished') {
        const furnishedKey = `properties.${property.furnished}` as const;
        lines.push('🔸 ' + t('marketingPost.furnishedText') + ': ' + t(furnishedKey));
      }
    }
    
    // Additional details
    if (property.view) {
      lines.push('🔸 ' + t('marketingPost.viewText') + ': ' + property.view);
    }
    
    if (property.finishingStatus) {
      lines.push('🔸 ' + t('marketingPost.finishingText') + ': ' + property.finishingStatus);
    }
    
    if (property.ownershipType) {
      lines.push('🔸 ' + t('marketingPost.ownershipText') + ': ' + property.ownershipType);
    }
    
    if (property.directions) {
      lines.push('');
      lines.push('🔸 ' + t('marketingPost.directionsText') + ':');
      lines.push(property.directions);
    }
    
    // Contact information
    lines.push('');
    if (property.ownerName || property.ownerPhone) {
      lines.push('📞 ' + t('marketingPost.contactText') + ':');
      if (property.ownerName) {
        lines.push('👤 ' + property.ownerName);
      }
      if (property.ownerPhone) {
        lines.push('📱 ' + property.ownerPhone);
      }
    }
    
    if (property.companyName) {
      lines.push('🏢 ' + t('marketingPost.companyText') + ': ' + property.companyName);
    }
    
    // Call to action
    lines.push('');
    lines.push(t('marketingPost.callToAction'));
    
    // Hashtags
    lines.push('');
    lines.push(t('marketingPost.hashtags'));
    
    return lines.join('\n');
  };

  return (
    <div className="space-y-4">
      <div className="bg-muted/50 p-4 rounded-lg border">
        <pre className="whitespace-pre-wrap text-sm font-mono leading-relaxed">
          {generateMarketingText()}
        </pre>
      </div>
    </div>
  );
}
