import { useState } from "react";
import { Co<PERSON>, Check, Share2, Edit3, Refresh<PERSON>w } from "lucide-react";
import { Property } from "@/types/property";
import { useTranslation } from "@/contexts/TranslationContext";
import { useExchangeRate } from "@/contexts/ExchangeRateContext";
import { useToast } from "@/components/ui/use-toast";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { MarketingPost } from "./MarketingPost";

interface MarketingPostModalProps {
  property: Property;
  isOpen: boolean;
  onClose: () => void;
}

export function MarketingPostModal({ property, isOpen, onClose }: MarketingPostModalProps) {
  const { t } = useTranslation();
  const { formatPrice } = useExchangeRate();
  const { toast } = useToast();
  const [copied, setCopied] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editedText, setEditedText] = useState("");

  const generateMarketingText = (): string => {
    const lines: string[] = [];
    
    // Title with status
    const statusText = property.status === 'for-sale' 
      ? t('marketingPost.forSaleText') 
      : t('marketingPost.forRentText');
    
    lines.push(`${statusText} ✨`);
    lines.push(`${property.title}`);
    lines.push('');
    
    // Description
    if (property.description) {
      lines.push(property.description);
      lines.push('');
    }
    
    // Key details
    lines.push('🔸 ' + t('marketingPost.locationText') + ': ' + property.location);
    lines.push('🔸 ' + t('marketingPost.priceText') + ': ' + formatPrice(property.price));
    lines.push('🔸 ' + t('marketingPost.sizeText') + ': ' + property.size + ' m²');
    
    // Property type
    const typeKey = `properties.${property.type}` as const;
    lines.push('🔸 ' + t('properties.type') + ': ' + t(typeKey));
    
    // Living details (if not land)
    if (property.type !== 'land') {
      if (property.distribution) {
        lines.push('🔸 ' + t('marketingPost.distributionText') + ': ' + property.distribution);
      } else if (property.bedrooms > 0) {
        lines.push('🔸 ' + t('marketingPost.bedroomsText') + ': ' + property.bedrooms);
      }
      
      if (property.bathrooms > 0) {
        lines.push('🔸 ' + t('marketingPost.bathroomsText') + ': ' + property.bathrooms);
      }
      
      if (property.floor !== null && (property.type === 'apartment' || property.type === 'commercial')) {
        lines.push('🔸 ' + t('marketingPost.floorText') + ': ' + property.floor);
      }
      
      if (property.furnished && property.furnished !== 'unfurnished') {
        const furnishedKey = `properties.${property.furnished}` as const;
        lines.push('🔸 ' + t('marketingPost.furnishedText') + ': ' + t(furnishedKey));
      }
    }
    
    // Additional details
    if (property.view) {
      lines.push('🔸 ' + t('marketingPost.viewText') + ': ' + property.view);
    }
    
    if (property.finishingStatus) {
      lines.push('🔸 ' + t('marketingPost.finishingText') + ': ' + property.finishingStatus);
    }
    
    if (property.ownershipType) {
      lines.push('🔸 ' + t('marketingPost.ownershipText') + ': ' + property.ownershipType);
    }
    
    if (property.directions) {
      lines.push('');
      lines.push('🔸 ' + t('marketingPost.directionsText') + ':');
      lines.push(property.directions);
    }
    
    // Contact information
    lines.push('');
    if (property.ownerName || property.ownerPhone) {
      lines.push('📞 ' + t('marketingPost.contactText') + ':');
      if (property.ownerName) {
        lines.push('👤 ' + property.ownerName);
      }
      if (property.ownerPhone) {
        lines.push('📱 ' + property.ownerPhone);
      }
    }
    
    if (property.companyName) {
      lines.push('🏢 ' + t('marketingPost.companyText') + ': ' + property.companyName);
    }
    
    // Call to action
    lines.push('');
    lines.push(t('marketingPost.callToAction'));
    
    // Hashtags
    lines.push('');
    lines.push(t('marketingPost.hashtags'));
    
    return lines.join('\n');
  };

  const marketingText = generateMarketingText();

  const handleCopy = async () => {
    try {
      const textToCopy = isEditing ? editedText : marketingText;
      await navigator.clipboard.writeText(textToCopy);
      setCopied(true);
      toast({
        title: t('marketingPost.postCopied'),
        description: t('marketingPost.postCopied'),
      });
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy text:', error);
      toast({
        title: t('common.error'),
        description: "Failed to copy to clipboard",
        variant: "destructive",
      });
    }
  };

  const handleEdit = () => {
    setIsEditing(true);
    setEditedText(marketingText);
  };

  const handleSaveEdit = () => {
    setIsEditing(false);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setEditedText("");
  };

  const handleGenerateNew = () => {
    setIsEditing(false);
    setEditedText("");
    toast({
      title: t('marketingPost.postGenerated'),
      description: t('marketingPost.postGenerated'),
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Share2 className="h-5 w-5" />
            {t('marketingPost.title')}
          </DialogTitle>
          <DialogDescription>
            {t('marketingPost.description')}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {isEditing ? (
            <div className="space-y-4">
              <Textarea
                value={editedText}
                onChange={(e) => setEditedText(e.target.value)}
                className="min-h-[400px] font-mono text-sm"
                placeholder="Edit your marketing post..."
              />
              <div className="flex gap-2">
                <Button onClick={handleSaveEdit} size="sm">
                  <Check className="h-4 w-4 mr-2" />
                  {t('common.save')}
                </Button>
                <Button onClick={handleCancelEdit} variant="outline" size="sm">
                  {t('common.cancel')}
                </Button>
              </div>
            </div>
          ) : (
            <div className="bg-muted/50 p-4 rounded-lg border">
              <pre className="whitespace-pre-wrap text-sm font-mono leading-relaxed">
                {marketingText}
              </pre>
            </div>
          )}
        </div>

        <DialogFooter className="flex flex-col sm:flex-row gap-2">
          <div className="flex gap-2 flex-1">
            <Button
              onClick={handleEdit}
              variant="outline"
              size="sm"
              disabled={isEditing}
            >
              <Edit3 className="h-4 w-4 mr-2" />
              {t('marketingPost.editPost')}
            </Button>
            <Button
              onClick={handleGenerateNew}
              variant="outline"
              size="sm"
              disabled={isEditing}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              {t('marketingPost.generateNew')}
            </Button>
          </div>
          <div className="flex gap-2">
            <Button onClick={handleCopy} className="gap-2">
              {copied ? (
                <Check className="h-4 w-4" />
              ) : (
                <Copy className="h-4 w-4" />
              )}
              {copied ? t('marketingPost.copied') : t('marketingPost.copyToClipboard')}
            </Button>
            <Button onClick={onClose} variant="outline">
              {t('common.close')}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
