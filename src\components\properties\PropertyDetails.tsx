
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  Building,
  MapPin,
  AreaChart,
  Hotel,
  Bath,
  Trash2,
  Edit,
  ArrowLeft,
  Send,
  User,
  Navigation,
  Eye,
  Home,
  Phone,
  Building2,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Property, PropertyNote } from "@/types/property";
import { useAuth } from "@/contexts/AuthContext";
import { useTranslation } from "@/contexts/TranslationContext";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Tabs,
  TabsContent,
  Ta<PERSON>List,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { ImageGallery } from "./ImageGallery";
import { useExchangeRate } from "@/contexts/ExchangeRateContext";

interface PropertyDetailsProps {
  property: Property;
  notes: PropertyNote[];
  onAddNote: (note: string) => void;
  onDelete: () => void;
}

export function PropertyDetails({
  property,
  notes,
  onAddNote,
  onDelete,
}: PropertyDetailsProps) {
  const navigate = useNavigate();
  const { user, isAdmin } = useAuth();
  const [newNote, setNewNote] = useState("");
  const { formatPrice } = useExchangeRate();
  const { t } = useTranslation();

  const handleAddNote = () => {
    if (newNote.trim()) {
      onAddNote(newNote.trim());
      setNewNote("");
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          size="sm"
          onClick={() => navigate(-1)}
          className="flex gap-2"
        >
          <ArrowLeft className="h-4 w-4" /> {t('propertyDetails.back')}
        </Button>

        {isAdmin() && (
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigate(`/properties/${property.id}/edit`)}
            >
              <Edit className="mr-2 h-4 w-4" /> {t('propertyDetails.edit')}
            </Button>

            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="destructive" size="sm">
                  <Trash2 className="mr-2 h-4 w-4" /> {t('propertyDetails.delete')}
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>{t('propertyDetails.areYouSure')}</AlertDialogTitle>
                  <AlertDialogDescription>
                    {t('propertyDetails.deleteConfirmation')}
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>{t('propertyDetails.cancel')}</AlertDialogCancel>
                  <AlertDialogAction onClick={onDelete}>
                    {t('propertyDetails.delete')}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        )}
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        <div className="md:col-span-2 space-y-6">
          <ImageGallery images={property.images} />

          <Tabs defaultValue="details">
            <TabsList>
              <TabsTrigger value="details">{t('propertyDetails.details')}</TabsTrigger>
              <TabsTrigger value="notes">
                {t('propertyDetails.notes')} ({notes.length})
              </TabsTrigger>
            </TabsList>

            <TabsContent value="details" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>{t('propertyDetails.description')}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p>{property.description}</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>{t('propertyDetails.features')}</CardTitle>
                </CardHeader>
                <CardContent className="grid gap-2 sm:grid-cols-2">
                  <div className="flex items-center gap-2">
                    <Building className="h-4 w-4 text-muted-foreground" />
                    <span>
                      {t('propertyDetails.type')}:{" "}
                      <span className="font-medium capitalize">
                        {t(`properties.${property.type}`)}
                      </span>
                    </span>
                  </div>

                  <div className="flex items-center gap-2">
                    <AreaChart className="h-4 w-4 text-muted-foreground" />
                    <span>
                      {t('propertyDetails.size')}: <span className="font-medium">{property.size} م²</span>
                    </span>
                  </div>

                  {property.type !== "land" && (
                    <>
                      <div className="flex items-center gap-2">
                        <Hotel className="h-4 w-4 text-muted-foreground" />
                        <span>
                          {t('propertyDetails.bedrooms')}:{" "}
                          <span className="font-medium">{property.bedrooms}</span>
                        </span>
                      </div>

                      <div className="flex items-center gap-2">
                        <Bath className="h-4 w-4 text-muted-foreground" />
                        <span>
                          {t('propertyDetails.bathrooms')}:{" "}
                          <span className="font-medium">
                            {property.bathrooms}
                          </span>
                        </span>
                      </div>

                      {(property.type === "apartment" ||
                        property.type === "commercial") && (
                        <div className="flex items-center gap-2">
                          <Building className="h-4 w-4 text-muted-foreground" />
                          <span>
                            {t('propertyDetails.floor')}:{" "}
                            <span className="font-medium">
                              {property.floor !== null ? property.floor : t('propertyDetails.notAvailable')}
                            </span>
                          </span>
                        </div>
                      )}

                      <div className="flex items-center gap-2">
                        <Home className="h-4 w-4 text-muted-foreground" />
                        <span>
                          {t('propertyDetails.furnished')}:{" "}
                          <span className="font-medium">
                            {t(`properties.${property.furnished}`)}
                          </span>
                        </span>
                      </div>
                    </>
                  )}

                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <span>
                      {t('propertyDetails.location')}:{" "}
                      <span className="font-medium">{property.location}</span>
                    </span>
                  </div>
                </CardContent>
              </Card>

              {/* Additional Details Card */}
              {(property.distribution || property.directions || property.finishingStatus ||
                property.ownershipType || property.view) && (
                <Card>
                  <CardHeader>
                    <CardTitle>{t('propertyDetails.additionalInfo')}</CardTitle>
                  </CardHeader>
                  <CardContent className="grid gap-2 sm:grid-cols-2">
                    {property.distribution && (
                      <div className="flex items-center gap-2 sm:col-span-2">
                        <Building2 className="h-4 w-4 text-muted-foreground" />
                        <span>
                          {t('propertyDetails.distribution')}:{" "}
                          <span className="font-medium">{property.distribution}</span>
                        </span>
                      </div>
                    )}

                    {property.directions && (
                      <div className="flex items-center gap-2 sm:col-span-2">
                        <Navigation className="h-4 w-4 text-muted-foreground" />
                        <span>
                          {t('propertyDetails.directions')}:{" "}
                          <span className="font-medium">{property.directions}</span>
                        </span>
                      </div>
                    )}

                    {property.view && (
                      <div className="flex items-center gap-2 sm:col-span-2">
                        <Eye className="h-4 w-4 text-muted-foreground" />
                        <span>
                          {t('propertyDetails.view')}:{" "}
                          <span className="font-medium">{property.view}</span>
                        </span>
                      </div>
                    )}

                    {property.finishingStatus && (
                      <div className="flex items-center gap-2">
                        <Building className="h-4 w-4 text-muted-foreground" />
                        <span>
                          {t('propertyDetails.finishingStatus')}:{" "}
                          <span className="font-medium">{property.finishingStatus}</span>
                        </span>
                      </div>
                    )}

                    {property.ownershipType && (
                      <div className="flex items-center gap-2">
                        <Building className="h-4 w-4 text-muted-foreground" />
                        <span>
                          {t('propertyDetails.ownershipType')}:{" "}
                          <span className="font-medium">{property.ownershipType}</span>
                        </span>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}

              {/* Contact Information Card */}
              {(property.ownerName || property.ownerPhone || property.companyName) && (
                <Card>
                  <CardHeader>
                    <CardTitle>{t('propertyDetails.contactInfo')}</CardTitle>
                  </CardHeader>
                  <CardContent className="grid gap-2 sm:grid-cols-2">
                    {property.ownerName && (
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4 text-muted-foreground" />
                        <span>
                          {t('propertyDetails.ownerName')}:{" "}
                          <span className="font-medium">{property.ownerName}</span>
                        </span>
                      </div>
                    )}

                    {property.ownerPhone && (
                      <div className="flex items-center gap-2">
                        <Phone className="h-4 w-4 text-muted-foreground" />
                        <span>
                          {t('propertyDetails.ownerPhone')}:{" "}
                          <span className="font-medium">{property.ownerPhone}</span>
                        </span>
                      </div>
                    )}

                    {property.companyName && (
                      <div className="flex items-center gap-2 sm:col-span-2">
                        <Building2 className="h-4 w-4 text-muted-foreground" />
                        <span>
                          {t('propertyDetails.companyName')}:{" "}
                          <span className="font-medium">{property.companyName}</span>
                        </span>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="notes">
              <Card>
                <CardHeader>
                  <CardTitle>{t('propertyDetails.propertyNotes')}</CardTitle>
                  <CardDescription>
                    {t('propertyDetails.internalNotes')}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {notes.length === 0 ? (
                    <div className="text-center p-4 text-muted-foreground">
                      {t('propertyDetails.noNotesYet')}
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {notes.map((note) => (
                        <div
                          key={note.id}
                          className="rounded-lg border p-4 space-y-2"
                        >
                          <p className="text-sm">{note.text}</p>
                          <div className="flex items-center justify-between text-xs text-muted-foreground">
                            <div className="flex items-center gap-1">
                              <User className="h-3 w-3" />
                              <span>{note.created_by}</span>
                            </div>
                            <div>{formatDate(note.created_at)}</div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}

                  {isAdmin() && (
                    <div className="flex gap-2 mt-4">
                      <Textarea
                        placeholder={t('propertyDetails.addNoteAboutProperty')}
                        className="flex-1"
                        value={newNote}
                        onChange={(e) => setNewNote(e.target.value)}
                      />
                      <Button onClick={handleAddNote}>
                        <Send className="mr-2 h-4 w-4" /> {t('propertyDetails.addNote')}
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>{property.title}</CardTitle>
              <CardDescription className="flex items-center gap-1">
                <MapPin className="h-4 w-4" /> {property.location}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold mb-4">
                {formatPrice(property.price)}
                {property.status === "for-rent" && (
                  <span className="text-sm font-normal">/{t('propertyDetails.month')}</span>
                )}
              </div>

              <div className="grid grid-cols-2 gap-y-2 text-sm">
                <div>{t('propertyDetails.status')}</div>
                <div className="font-medium">
                  {property.status === "for-sale" ? t('propertyDetails.forSale') : t('propertyDetails.forRent')}
                </div>

                <div>{t('propertyDetails.propertyType')}</div>
                <div className="font-medium">{t(`properties.${property.type}`)}</div>

                <div>{t('propertyDetails.created')}</div>
                <div className="font-medium">
                  {formatDate(property.createdAt)}
                </div>

                <div>{t('propertyDetails.lastUpdated')}</div>
                <div className="font-medium">
                  {formatDate(property.updatedAt)}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
