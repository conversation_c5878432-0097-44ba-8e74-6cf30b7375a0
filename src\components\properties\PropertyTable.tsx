import { useState, useMemo } from "react";
import { Link } from "react-router-dom";
import {
  Edit, Trash2, Search, ArrowUpDown,
  ChevronDown, Eye
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Property, PropertyStatus, PropertyType } from "@/types/property";
import { useAuth } from "@/contexts/AuthContext";
import { useTranslation } from "@/contexts/TranslationContext";
import { useExchangeRate } from "@/contexts/ExchangeRateContext";

interface PropertyTableProps {
  properties: Property[];
  onDelete: (id: string) => void;
}

export function PropertyTable({ properties, onDelete }: PropertyTableProps) {
  const { isAdmin } = useAuth();
  const { t } = useTranslation();
  const { formatPrice } = useExchangeRate();
  const [searchTerm, setSearchTerm] = useState("");
  const [sortField, setSortField] = useState<keyof Property>("createdAt");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");
  const [statusFilter, setStatusFilter] = useState<PropertyStatus | "all">("all");
  const [typeFilter, setTypeFilter] = useState<PropertyType | "all">("all");

  const handleSort = (field: keyof Property) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("asc");
    }
  };

  const filteredProperties = useMemo(() => {
    return properties.filter(property => {
      // Filter by search term
      const matchesSearch =
        property.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        property.location.toLowerCase().includes(searchTerm.toLowerCase());

      // Filter by status
      const matchesStatus =
        statusFilter === "all" || property.status === statusFilter;

      // Filter by type
      const matchesType =
        typeFilter === "all" || property.type === typeFilter;

      return matchesSearch && matchesStatus && matchesType;
    })
    .sort((a, b) => {
      if (sortField === "price" || sortField === "size") {
        return sortDirection === "asc"
          ? a[sortField] - b[sortField]
          : b[sortField] - a[sortField];
      }

      const valA = a[sortField]?.toString() || "";
      const valB = b[sortField]?.toString() || "";

      return sortDirection === "asc"
        ? valA.localeCompare(valB)
        : valB.localeCompare(valA);
    });
  }, [properties, searchTerm, sortField, sortDirection, statusFilter, typeFilter]);

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex items-center relative flex-1">
          <Search className="absolute left-3 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder={`${t('common.search')} ${t('properties.title')}...`}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 max-w-sm"
          />
        </div>

        <div className="flex gap-2">
          <Select
            value={statusFilter}
            onValueChange={(value) => setStatusFilter(value as PropertyStatus | "all")}
          >
            <SelectTrigger className="w-[160px]">
              <SelectValue placeholder={t('properties.status')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('common.all')} {t('properties.status')}</SelectItem>
              <SelectItem value="for-sale">{t('properties.forSale')}</SelectItem>
              <SelectItem value="for-rent">{t('properties.forRent')}</SelectItem>
            </SelectContent>
          </Select>

          <Select
            value={typeFilter}
            onValueChange={(value) => setTypeFilter(value as PropertyType | "all")}
          >
            <SelectTrigger className="w-[160px]">
              <SelectValue placeholder={t('properties.type')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('common.all')} {t('properties.type')}</SelectItem>
              <SelectItem value="apartment">{t('properties.apartment')}</SelectItem>
              <SelectItem value="house">{t('properties.house')}</SelectItem>
              <SelectItem value="villa">{t('properties.villa')}</SelectItem>
              <SelectItem value="land">{t('properties.land')}</SelectItem>
              <SelectItem value="commercial">{t('properties.commercial')}</SelectItem>
              <SelectItem value="other">{t('properties.other')}</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>
                <Button
                  variant="ghost"
                  onClick={() => handleSort("title")}
                >
                  {t('properties.propertyTitle')} <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
              </TableHead>
              <TableHead>{t('properties.status')}</TableHead>
              <TableHead>{t('properties.type')}</TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  onClick={() => handleSort("location")}
                >
                  {t('properties.location')} <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
              </TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  onClick={() => handleSort("price")}
                >
                  {t('properties.price')} <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
              </TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  onClick={() => handleSort("size")}
                >
                  {t('properties.size')} (م²) <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
              </TableHead>
              <TableHead className="text-right">{t('common.actions')}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredProperties.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="h-24 text-center">
                  {t('properties.noProperties')}
                </TableCell>
              </TableRow>
            ) : (
              filteredProperties.map((property) => (
                <TableRow key={property.id}>
                  <TableCell>
                    <div className="font-medium">{property.title}</div>
                  </TableCell>
                  <TableCell>
                    <div className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-semibold ${
                      property.status === "for-sale"
                        ? "bg-blue-100 text-blue-800"
                        : "bg-green-100 text-green-800"
                    }`}>
                      {property.status === "for-sale" ? t('properties.forSale') : t('properties.forRent')}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="capitalize">{property.type}</div>
                  </TableCell>
                  <TableCell>{property.location}</TableCell>
                  <TableCell>
                    {formatPrice(property.price)}
                    {property.status === "for-rent" && `/${t('properties.month')}`}
                  </TableCell>
                  <TableCell>{property.size}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button variant="ghost" size="icon" asChild>
                        <Link to={`/properties/${property.id}`}>
                          <Eye className="h-4 w-4" />
                          <span className="sr-only">{t('common.view')}</span>
                        </Link>
                      </Button>

                      {isAdmin() && (
                        <>
                          <Button variant="ghost" size="icon" asChild>
                            <Link to={`/properties/${property.id}/edit`}>
                              <Edit className="h-4 w-4" />
                              <span className="sr-only">{t('common.edit')}</span>
                            </Link>
                          </Button>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <Trash2 className="h-4 w-4 text-red-500" />
                                <span className="sr-only">{t('common.delete')}</span>
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem
                                className="text-red-600"
                                onClick={() => onDelete(property.id)}
                              >
                                {t('common.confirm')} {t('common.delete')}
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
