import React, { createContext, useContext, ReactNode, useState, useEffect } from 'react';
import { ar } from '../translations/ar';
import { en } from '../translations/en';

// Define supported languages
type Language = 'en' | 'ar';

// Define the shape of our translation context
type TranslationContextType = {
  t: (key: string) => string;
  language: Language;
  setLanguage: (language: Language) => void;
};

// Create the context with a default value
const TranslationContext = createContext<TranslationContextType>({
  t: () => '',
  language: 'ar',
  setLanguage: () => {},
});

// Create a provider component
export const TranslationProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  // State for current language, defaulting to Arabic but checking localStorage
  const [language, setLanguageState] = useState<Language>(() => {
    const savedLanguage = localStorage.getItem('language') as Language;
    return savedLanguage && ['en', 'ar'].includes(savedLanguage) ? savedLanguage : 'ar';
  });

  // Update localStorage when language changes
  useEffect(() => {
    localStorage.setItem('language', language);
  }, [language]);

  // Function to change language
  const setLanguage = (newLanguage: Language) => {
    setLanguageState(newLanguage);
  };

  // Get the appropriate translation object
  const getTranslations = () => {
    return language === 'en' ? en : ar;
  };

  // Function to get a translation by key
  const t = (key: string): string => {
    // Split the key by dots to navigate the nested structure
    const keys = key.split('.');

    // Start with the current language translations
    let value: any = getTranslations();

    // Navigate through the nested structure
    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k];
      } else {
        // If the key doesn't exist, return the key itself
        console.warn(`Translation key not found: ${key} for language: ${language}`);
        return key;
      }
    }

    // Return the translation or the key if the value is not a string
    return typeof value === 'string' ? value : key;
  };

  return (
    <TranslationContext.Provider value={{ t, language, setLanguage }}>
      {children}
    </TranslationContext.Provider>
  );
};

// Create a hook to use the translation context
export const useTranslation = () => {
  const context = useContext(TranslationContext);

  if (!context) {
    throw new Error('useTranslation must be used within a TranslationProvider');
  }

  return context;
};
