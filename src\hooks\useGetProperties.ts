
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { Property } from '@/types/property';
import { transformProperty } from '@/utils/propertyTransformers';

// Hook to fetch all properties
export const useProperties = () => {
  return useQuery({
    queryKey: ['properties'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('properties')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;

      return data.map(transformProperty);
    }
  });
};

// Hook to fetch a single property
export const useProperty = (id: string) => {
  return useQuery({
    queryKey: ['property', id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('properties')
        .select('*')
        .eq('id', id)
        .single();

      if (error) throw error;

      return transformProperty(data);
    },
    enabled: !!id
  });
};
