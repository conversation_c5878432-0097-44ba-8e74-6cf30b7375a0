
import React from 'react';
import ReactDOM from 'react-dom/client';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Toaster } from '@/components/ui/sonner';
import { handleAuthError } from '@/lib/authUtils';
import App from './App';
import './index.css';

// Import mock handlers for development
import './mocks/handlers';

// Create a query client with error handling
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: (failureCount, error: any) => {
        // Don't retry if it's an auth error
        if (error?.message?.includes('Invalid Refresh Token')) {
          return false;
        }
        // Otherwise retry up to 3 times
        return failureCount < 3;
      },
      refetchOnWindowFocus: false,
    },
    mutations: {
      onError: (error: any) => {
        // Handle auth errors globally
        handleAuthError(error);
      }
    }
  }
});

// Add error handling for React errors
window.addEventListener('error', (event) => {
  console.error('Global error caught:', event.error);
});

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <BrowserRouter>
      <QueryClientProvider client={queryClient}>
        <App />
        <Toaster position="bottom-right" />
      </QueryClientProvider>
    </BrowserRouter>
  </React.StrictMode>,
);
