
// This file sets up development mock API handlers

export const setupMockHandlers = () => {
  // Mock the exchange rate API
  const mockExchangeRateHandler = async (req: Request) => {
    try {
      // Simulate delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Return mock data that matches the CurrencyFreaks API format
      return new Response(
        JSON.stringify({
          base: "USD",
          date: new Date().toISOString().split('T')[0],
          rates: {
            SYP: "13500" // Mock exchange rate (would be fetched from API in production)
          }
        }),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );
    } catch (error) {
      console.error("Mock handler error:", error);
      return new Response(
        JSON.stringify({ error: "Mock API error" }),
        { 
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
  };

  // Register handlers
  if (window) {
    window.addEventListener('fetch', (event: any) => {
      if (event.request.url.includes('/api/exchange-rate')) {
        event.respondWith(mockExchangeRateHandler(event.request));
      }
    }, { capture: true });
  }
};

// Initialize mock handlers if we're in development
if (process.env.NODE_ENV === 'development') {
  setupMockHandlers();
  console.log("Mock API handlers initialized");
}
