
import { useCallback, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { Layout } from "@/components/layout/Layout";
import { PropertyForm } from "@/components/properties/PropertyForm";
import { MarketingPostModal } from "@/components/properties/MarketingPostModal";
import { useToast } from "@/components/ui/use-toast";
import { useProperty, useUpdateProperty } from "@/hooks/useProperties";
import { useTranslation } from "@/contexts/TranslationContext";
import { Property } from "@/types/property";

const EditProperty = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { t } = useTranslation();
  const [showMarketingPost, setShowMarketingPost] = useState(false);
  const [updatedProperty, setUpdatedProperty] = useState<Property | null>(null);

  const {
    data: property,
    isLoading,
    error
  } = useProperty(id || "");

  const updatePropertyMutation = useUpdateProperty();

  const handleSubmit = useCallback((data: any) => {
    if (!id) return;

    updatePropertyMutation.mutate(
      { id, ...data },
      {
        onSuccess: (updatedProp) => {
          toast({
            title: t('propertyForm.editProperty'),
            description: t('properties.deletedSuccess'),
          });

          // Set the updated property and show marketing post modal
          setUpdatedProperty(updatedProp);
          setShowMarketingPost(true);
        },
        onError: (error) => {
          toast({
            title: t('common.error'),
            description: `${t('properties.deleteError')}: ${error.message}`,
            variant: "destructive",
          });
        }
      }
    );
  }, [id, updatePropertyMutation, navigate, toast, t]);

  const handleCloseMarketingPost = () => {
    setShowMarketingPost(false);
    setUpdatedProperty(null);
    navigate(`/properties/${id}`);
  };

  if (isLoading) {
    return (
      <Layout>
        <div className="flex flex-col items-center justify-center h-[60vh]">
          <p className="text-lg text-muted-foreground">{t('common.loading')}</p>
        </div>
      </Layout>
    );
  }

  if (error || !property) {
    return (
      <Layout>
        <div className="flex flex-col items-center justify-center h-[60vh]">
          <h1 className="text-2xl font-bold">{t('propertyDetails.propertyNotFound')}</h1>
          <p className="text-muted-foreground">
            {t('propertyDetails.propertyNotFoundDesc')}
          </p>
          <button
            onClick={() => navigate("/properties")}
            className="mt-4 text-blue-500 hover:underline"
          >
            {t('propertyDetails.backToProperties')}
          </button>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t('propertyForm.editProperty')}</h1>
          <p className="text-muted-foreground">
            {t('propertyForm.updateListing')}
          </p>
        </div>

        <PropertyForm
          property={property}
          onSubmit={handleSubmit}
          isSubmitting={updatePropertyMutation.isPending}
        />
      </div>

      {/* Marketing Post Modal */}
      {updatedProperty && (
        <MarketingPostModal
          property={updatedProperty}
          isOpen={showMarketingPost}
          onClose={handleCloseMarketingPost}
        />
      )}
    </Layout>
  );
};

export default EditProperty;
