
import { useState, useEffect } from "react";
import { Layout } from "@/components/layout/Layout";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { useTranslation } from "@/contexts/TranslationContext";
import { useExchangeRate } from "@/contexts/ExchangeRateContext";
import { toast } from "sonner";

const Settings = () => {
  const { t } = useTranslation();
  const {
    currency,
    setCurrency,
    exchangeRate,
    refreshExchangeRate,
    lastUpdated,
    rateSource
  } = useExchangeRate();

  const [isLoading, setIsLoading] = useState(false);

  const handleCurrencyChange = (checked: boolean) => {
    try {
      setCurrency(checked ? 'SYP' : 'USD');
    } catch (error) {
      console.error("Error changing currency:", error);
      toast.error("Failed to change currency");
    }
  };

  const handleRefreshRate = async () => {
    try {
      setIsLoading(true);
      await refreshExchangeRate();
    } catch (error) {
      console.error("Error refreshing rate:", error);
      toast.error("Failed to refresh exchange rate");
    } finally {
      setIsLoading(false);
    }
  };

  const formatLastUpdated = () => {
    if (!lastUpdated) return t('settings.never');

    const date = new Date(lastUpdated);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
    }).format(date);
  };

  return (
    <Layout>
      <div className="container mx-auto py-6">
        <h1 className="text-3xl font-bold mb-6">{t('settings.title')}</h1>

        <div className="grid gap-6 md:grid-cols-2">
          {/* Currency Settings */}
          <Card>
            <CardHeader>
              <CardTitle>{t('settings.currency')}</CardTitle>
              <CardDescription>{t('settings.currencyDescription')}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label>{t('settings.displayCurrency')}</Label>
                  <p className="text-sm text-gray-500">{t('settings.currencyToggleDescription')}</p>
                </div>
                <Switch
                  checked={currency === 'SYP'}
                  onCheckedChange={handleCurrencyChange}
                />
              </div>

              <div className="flex flex-col space-y-1">
                <Label>{t('settings.currentRate')}</Label>
                <div className="text-lg font-semibold">
                  1 USD = {exchangeRate ? exchangeRate.toLocaleString() : 'Loading...'} SYP
                </div>
                <p className="text-sm text-gray-500">
                  {t('settings.lastUpdated')}: {formatLastUpdated()}
                </p>
                <p className="text-xs text-gray-400">
                  {t('settings.source')}: {rateSource}
                </p>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={handleRefreshRate} disabled={isLoading}>
                {isLoading ? t('common.loading') : t('settings.updateRate')}
              </Button>
            </CardFooter>
          </Card>

          {/* We'll keep the app settings card but remove the language switching since it's not implemented */}
          <Card>
            <CardHeader>
              <CardTitle>{t('settings.appSettings')}</CardTitle>
              <CardDescription>{t('settings.appSettingsDescription')}</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-500">{t('settings.comingSoon')}</p>
            </CardContent>
          </Card>
        </div>
      </div>
    </Layout>
  );
};

export default Settings;
