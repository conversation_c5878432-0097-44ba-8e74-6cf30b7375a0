// Arabic translations
export const ar = {
  // Common
  common: {
    search: "بحث",
    submit: "إرسال",
    cancel: "إلغاء",
    save: "حفظ",
    edit: "تعديل",
    delete: "حذف",
    back: "رجوع",
    loading: "جاري التحميل...",
    error: "خطأ",
    success: "نجاح",
    welcome: "مرحبًا",
    yes: "نعم",
    no: "لا",
    more: "المزيد",
    all: "الكل",
    actions: "إجراءات",
    details: "التفاصيل",
    view: "عرض",
    close: "إغلاق",
    confirm: "تأكيد",
    guest: "زائر",
    user: "مستخدم",
    notLoggedIn: "غير مسجل الدخول",
    select: "اختر",
    language: "اللغة",
    theme: "المظهر",
    lightMode: "المظهر الفاتح",
    darkMode: "المظهر الداكن",
    english: "الإنجليزية",
    arabic: "العربية",
  },

  // Layout
  layout: {
    dashboard: "لوحة التحكم",
    properties: "العقارات",
    settings: "الإعدادات",
    users: "المستخدمين",
    logout: "تسجيل الخروج",
    appName: "العقارات برو",
    propertyOffers: "عروض العقارات",
    customerRequests: "طلبات العملاء",
  },

  // Auth
  auth: {
    login: "تسجيل الدخول",
    register: "إنشاء حساب",
    email: "البريد الإلكتروني",
    password: "كلمة المرور",
    confirmPassword: "تأكيد كلمة المرور",
    forgotPassword: "نسيت كلمة المرور؟",
    loginToAccount: "تسجيل الدخول إلى حسابك",
    createAccount: "إنشاء حساب جديد",
    signUpToStart: "سجل للبدء في إدارة العقارات",
    allFieldsRequired: "جميع الحقول مطلوبة",
    passwordsDoNotMatch: "كلمات المرور غير متطابقة",
    passwordMinLength: "يجب أن تكون كلمة المرور 6 أحرف على الأقل",
    authRequired: "مطلوب المصادقة",
    loginRequired: "يجب تسجيل الدخول للوصول إلى هذه الصفحة",
    dontHaveAccount: "ليس لديك حساب؟",
    alreadyHaveAccount: "لديك حساب بالفعل؟",
  },

  // Dashboard
  dashboard: {
    title: "لوحة التحكم",
    welcomeBack: "مرحبًا بعودتك",
    totalProperties: "إجمالي العقارات",
    forSale: "للبيع",
    forRent: "للإيجار",
    totalValue: "القيمة الإجمالية",
    recentProperties: "العقارات الحديثة",
    featuredProperties: "العقارات المميزة",
    viewAll: "عرض الكل",
    addProperty: "إضافة عقار",
    highlightedProperties: "العقارات البارزة في محفظتك",
  },

  // Properties
  properties: {
    title: "العقارات",
    addProperty: "إضافة عقار",
    propertyListings: "قائمة العقارات",
    viewAndManage: "عرض وإدارة جميع العقارات",
    tableView: "عرض الجدول",
    gridView: "عرض الشبكة",
    total: "الإجمالي",
    noProperties: "لا توجد عقارات",
    propertyTitle: "عنوان العقار",
    status: "الحالة",
    type: "النوع",
    location: "الموقع",
    price: "السعر",
    forSale: "للبيع",
    forRent: "للإيجار",
    apartment: "شقة",
    house: "منزل",
    villa: "فيلا",
    land: "أرض",
    commercial: "تجاري",
    other: "أخرى",
    beds: "غرف",
    bed: "غرفة",
    baths: "حمامات",
    bath: "حمام",
    size: "المساحة",
    floor: "الطابق",
    furnished: "مفروش",
    semiFurnished: "شبه مفروش",
    unfurnished: "غير مفروش",
    month: "شهر",
    managePortfolio: "إدارة محفظة العقارات الخاصة بك",
    deleted: "تم حذف العقار",
    deletedSuccess: "تم حذف العقار بنجاح",
    deleteError: "فشل في حذف العقار",
    loadError: "خطأ في تحميل العقارات",
  },

  // Property Details
  propertyDetails: {
    title: "تفاصيل العقار",
    description: "الوصف",
    features: "المميزات",
    location: "الموقع",
    notes: "الملاحظات",
    addNote: "إضافة ملاحظة",
    deleteProperty: "حذف العقار",
    editProperty: "تعديل العقار",
    confirmDelete: "هل أنت متأكد من حذف هذا العقار؟",
    deleteWarning: "لا يمكن التراجع عن هذا الإجراء.",
    propertyNotFound: "العقار غير موجود",
    propertyNotFoundDesc: "العقار الذي تبحث عنه غير موجود أو تم حذفه.",
    backToProperties: "العودة إلى العقارات",
    overview: "نظرة عامة",
    gallery: "معرض الصور",
    writeNote: "اكتب ملاحظة...",
    sendNote: "إرسال",
    noNotes: "لا توجد ملاحظات بعد",
    addedBy: "أضيف بواسطة",
    back: "رجوع",
    edit: "تعديل",
    delete: "حذف",
    areYouSure: "هل أنت متأكد؟",
    deleteConfirmation: "لا يمكن التراجع عن هذا الإجراء. سيتم حذف هذا العقار والبيانات المرتبطة به نهائياً.",
    cancel: "إلغاء",
    details: "التفاصيل",
    propertyNotes: "ملاحظات العقار",
    internalNotes: "ملاحظات داخلية حول هذا العقار.",
    noNotesYet: "لا توجد ملاحظات بعد.",
    addNoteAboutProperty: "أضف ملاحظة حول هذا العقار...",
    type: "النوع",
    size: "المساحة",
    bedrooms: "غرف النوم",
    bathrooms: "الحمامات",
    floor: "الطابق",
    furnished: "مفروش",
    status: "الحالة",
    propertyType: "نوع العقار",
    created: "تم الإنشاء",
    lastUpdated: "آخر تحديث",
    forSale: "للبيع",
    forRent: "للإيجار",
    month: "شهر",
    // New fields
    distribution: "التوزيع",
    directions: "الاتجاهات",
    finishingStatus: "حالة التشطيب",
    ownershipType: "نوع الملكية",
    ownerName: "اسم المالك",
    ownerPhone: "هاتف المالك",
    view: "الإطلالة",
    companyName: "اسم الشركة",
    contactInfo: "معلومات الاتصال",
    additionalInfo: "معلومات إضافية",
    notAvailable: "غير متوفر",
  },

  // Add/Edit Property
  propertyForm: {
    addNew: "إضافة عقار جديد",
    editProperty: "تعديل العقار",
    createListing: "إنشاء قائمة عقار جديدة مع التفاصيل",
    updateListing: "تحديث قائمة العقار مع التفاصيل",
    basicInfo: "المعلومات الأساسية",
    propertyTitle: "عنوان العقار",
    enterTitle: "أدخل عنوان العقار",
    description: "الوصف",
    describeProperty: "وصف العقار",
    price: "السعر",
    enterPrice: "أدخل السعر",
    location: "الموقع",
    enterLocation: "أدخل الموقع",
    details: "التفاصيل",
    bedrooms: "غرف النوم",
    bathrooms: "الحمامات",
    floor: "الطابق",
    size: "المساحة (م²)",
    status: "الحالة",
    type: "النوع",
    furnished: "مفروش",
    images: "الصور",
    uploadImages: "تحميل الصور",
    dragAndDrop: "اسحب وأفلت الصور هنا أو انقر للتصفح",
    maxSize: "الحد الأقصى للحجم: 5 ميجابايت لكل صورة",
    authRequired: "مطلوب المصادقة",
    loginRequired: "يجب تسجيل الدخول لإنشاء عقار.",
    errorOccurred: "حدث خطأ",
    submitting: "جاري الإرسال...",
    propertyImages: "صور العقار",
    addImage: "إضافة صورة",
    uploadFormats: "تحميل حتى {maxImages} صور. الصيغ المدعومة: JPG، PNG، GIF",
    // New fields
    distribution: "التوزيع",
    enterDistribution: "أدخل تفاصيل التوزيع والتخطيط",
    directions: "الاتجاهات",
    enterDirections: "أدخل الاتجاهات إلى العقار",
    finishingStatus: "حالة التشطيب",
    enterFinishingStatus: "أدخل حالة التشطيب",
    ownershipType: "نوع الملكية",
    enterOwnershipType: "أدخل نوع الملكية",
    ownerName: "اسم المالك",
    enterOwnerName: "أدخل اسم المالك",
    ownerPhone: "هاتف المالك",
    enterOwnerPhone: "أدخل رقم هاتف المالك",
    view: "الإطلالة",
    enterView: "أدخل وصف الإطلالة",
    companyName: "اسم الشركة",
    enterCompanyName: "أدخل اسم الشركة",
    contactInfo: "معلومات الاتصال",
    additionalDetails: "تفاصيل إضافية",
  },

  // Not Found
  notFound: {
    title: "404",
    message: "عفوًا! الصفحة غير موجودة",
    returnHome: "العودة إلى الصفحة الرئيسية",
  },

  // Settings
  settings: {
    title: "الإعدادات",
    currency: "العملة",
    currencyDescription: "إدارة إعدادات العملة",
    displayCurrency: "عرض العملة",
    currencyToggleDescription: "تبديل بين الدولار الأمريكي والليرة السورية",
    currentRate: "سعر الصرف الحالي",
    lastUpdated: "آخر تحديث",
    never: "أبدًا",
    updateRate: "تحديث السعر",
    appSettings: "إعدادات التطبيق",
    appSettingsDescription: "إدارة إعدادات التطبيق",
    comingSoon: "قريبًا...",
    source: "المصدر",
  },

  // Validation
  validation: {
    required: "هذا الحقل مطلوب",
    minLength: "يجب أن يكون على الأقل {min} أحرف",
    maxLength: "يجب أن يكون أقل من {max} حرف",
    email: "يرجى إدخال بريد إلكتروني صالح",
    number: "يرجى إدخال رقم صالح",
    positive: "يجب أن يكون رقمًا موجبًا",
    integer: "يجب أن يكون رقمًا صحيحًا",
    match: "يجب أن تتطابق القيم",
  },

  // Property Offers
  propertyOffers: {
    title: "عروض العقارات",
    searchPlaceholder: "البحث في العقارات...",
    propertyType: "نوع العقار",
    status: "الحالة",
    allTypes: "جميع الأنواع",
    allStatuses: "جميع الحالات",
    resetFilters: "إعادة تعيين المرشحات",
    errorLoading: "خطأ في تحميل العقارات",
    noPropertiesMatch: "لا توجد عقارات تطابق معاييرك.",
    propertyDetailsOverview: "نظرة عامة على تفاصيل العقارات",
    title_column: "العنوان",
    location_column: "الموقع",
    price_column: "السعر",
    type_column: "النوع",
    status_column: "الحالة",
    forSale: "للبيع",
    forRent: "للإيجار",
  },

  // Customer Requests
  customerRequests: {
    title: "تقديم طلب عقار",
    subtitle: "أخبرنا عما تبحث عنه",
    propertyType: "نوع العقار",
    preferredLocation: "الموقع المفضل",
    minPrice: "الحد الأدنى للسعر (دولار أمريكي)",
    maxPrice: "الحد الأقصى للسعر (دولار أمريكي)",
    minBedrooms: "الحد الأدنى لغرف النوم",
    minBathrooms: "الحد الأدنى للحمامات",
    minSize: "الحد الأدنى للمساحة (م²)",
    contactPreference: "طريقة الاتصال المفضلة",
    additionalDetails: "تفاصيل إضافية",
    detailsPlaceholder: "يرجى وصف أي متطلبات أو تفضيلات إضافية...",
    submitRequest: "تقديم الطلب",
    submitting: "جاري التقديم...",
    requestSubmitted: "تم تقديم الطلب",
    requestSubmittedDesc: "تم تقديم طلب العقار الخاص بك بنجاح.",
    authError: "خطأ في المصادقة",
    authErrorDesc: "يجب تسجيل الدخول لتقديم طلب",
    error: "خطأ",
    errorDesc: "حدث خطأ أثناء تقديم طلبك. يرجى المحاولة مرة أخرى.",
    selectPropertyType: "اختر نوع العقار",
    enterLocation: "أدخل الموقع",
    selectContactMethod: "اختر طريقة الاتصال",
    email: "البريد الإلكتروني",
    phone: "الهاتف",
    requestsList: "قائمة طلبات العقارات",
    tableView: "عرض الجدول",
    cardView: "عرض البطاقات",
    total: "الإجمالي",
    requests: "طلبات",
    noRequests: "لا توجد طلبات عقارات",
    statusUpdated: "تم تحديث الحالة",
    statusUpdatedDesc: "تم تحديث حالة الطلب إلى {status}.",
    updateStatusError: "فشل في تحديث الحالة",
    updateStatus: "تحديث الحالة",
    markAsOpen: "تعيين كمفتوح",
    markAsInProgress: "تعيين قيد التنفيذ",
    markAsMatched: "تعيين كمطابق",
    markAsClosed: "تعيين كمغلق",
    status: {
      open: "مفتوح",
      inProgress: "قيد التنفيذ",
      matched: "مطابق",
      closed: "مغلق"
    },
    requestedOn: "تم الطلب في",
    priceRange: "نطاق السعر",
    contactMethod: "طريقة الاتصال",
  },
};
