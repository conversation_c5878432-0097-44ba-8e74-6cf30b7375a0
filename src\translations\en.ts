// English translations
export const en = {
  // Common
  common: {
    search: "Search",
    submit: "Submit",
    cancel: "Cancel",
    save: "Save",
    edit: "Edit",
    delete: "Delete",
    back: "Back",
    loading: "Loading...",
    error: "Error",
    success: "Success",
    welcome: "Welcome",
    yes: "Yes",
    no: "No",
    more: "More",
    all: "All",
    actions: "Actions",
    details: "Details",
    view: "View",
    close: "Close",
    confirm: "Confirm",
    guest: "Guest",
    user: "User",
    notLoggedIn: "Not logged in",
    select: "Select",
    language: "Language",
    theme: "Theme",
    lightMode: "Light Mode",
    darkMode: "Dark Mode",
    english: "English",
    arabic: "Arabic",
  },

  // Layout
  layout: {
    dashboard: "Dashboard",
    properties: "Properties",
    settings: "Settings",
    users: "Users",
    logout: "Logout",
    appName: "Property Pro",
    propertyOffers: "Property Offers",
    customerRequests: "Customer Requests",
  },

  // Auth
  auth: {
    login: "Login",
    register: "Register",
    email: "Email",
    password: "Password",
    confirmPassword: "Confirm Password",
    forgotPassword: "Forgot Password?",
    loginToAccount: "Login to your account",
    createAccount: "Create new account",
    signUpToStart: "Sign up to start managing properties",
    allFieldsRequired: "All fields are required",
    passwordsDoNotMatch: "Passwords do not match",
    passwordMinLength: "Password must be at least 6 characters",
    authRequired: "Authentication required",
    loginRequired: "You must be logged in to access this page",
    dontHaveAccount: "Don't have an account?",
    alreadyHaveAccount: "Already have an account?",
  },

  // Dashboard
  dashboard: {
    title: "Dashboard",
    welcomeBack: "Welcome back",
    totalProperties: "Total Properties",
    forSale: "For Sale",
    forRent: "For Rent",
    totalValue: "Total Value",
    recentProperties: "Recent Properties",
    featuredProperties: "Featured Properties",
    viewAll: "View All",
    addProperty: "Add Property",
    highlightedProperties: "Highlighted properties in your portfolio",
  },

  // Properties
  properties: {
    title: "Properties",
    addProperty: "Add Property",
    propertyListings: "Property Listings",
    viewAndManage: "View and manage all properties",
    tableView: "Table View",
    gridView: "Grid View",
    total: "Total",
    noProperties: "No properties found",
    propertyTitle: "Property Title",
    status: "Status",
    type: "Type",
    location: "Location",
    price: "Price",
    forSale: "For Sale",
    forRent: "For Rent",
    apartment: "Apartment",
    house: "House",
    villa: "Villa",
    land: "Land",
    commercial: "Commercial",
    other: "Other",
    beds: "beds",
    bed: "bed",
    baths: "baths",
    bath: "bath",
    size: "Size",
    floor: "Floor",
    furnished: "Furnished",
    semiFurnished: "Semi-furnished",
    unfurnished: "Unfurnished",
    month: "month",
    managePortfolio: "Manage your property portfolio",
    deleted: "Property deleted",
    deleteConfirm: "Are you sure you want to delete this property?",
    deleteDescription: "This action cannot be undone. This will permanently delete the property from your portfolio.",
    editProperty: "Edit Property",
    viewProperty: "View Property",
    propertyDetails: "Property Details",
    contactOwner: "Contact Owner",
    ownerInfo: "Owner Information",
    propertyFeatures: "Property Features",
    propertyImages: "Property Images",
    noImages: "No images available",
    imageGallery: "Image Gallery",
    propertyOverview: "Property Overview",
    keyFeatures: "Key Features",
    additionalInfo: "Additional Information",
    propertyDescription: "Property Description",
    contactDetails: "Contact Details",
    ownerName: "Owner Name",
    phoneNumber: "Phone Number",
    companyName: "Company Name",
    viewDescription: "View Description",
    directions: "Directions",
    finishing: "Finishing Status",
    ownership: "Ownership Type",
    distribution: "Distribution",
  },

  // Property Form
  propertyForm: {
    addNew: "Add New Property",
    editProperty: "Edit Property",
    createListing: "Create a new property listing with details",
    updateListing: "Update property listing with details",
    basicInfo: "Basic Information",
    propertyTitle: "Property Title",
    enterTitle: "Enter property title",
    description: "Description",
    describeProperty: "Describe the property",
    price: "Price",
    enterPrice: "Enter price",
    location: "Location",
    enterLocation: "Enter location",
    details: "Details",
    bedrooms: "Bedrooms",
    bathrooms: "Bathrooms",
    floor: "Floor",
    size: "Size (m²)",
    status: "Status",
    type: "Type",
    furnished: "Furnished",
    images: "Images",
    uploadImages: "Upload Images",
    dragAndDrop: "Drag and drop images here or click to browse",
    maxSize: "Maximum size: 5MB per image",
    supportedFormats: "Supported formats: JPG, PNG, WebP",
    removeImage: "Remove image",
    propertyType: "Property Type",
    enterPropertyType: "Enter property type",
    distribution: "Distribution",
    enterDistribution: "Enter distribution details",
    directions: "Directions",
    enterDirections: "Enter directions",
    finishingStatus: "Finishing Status",
    enterFinishingStatus: "Enter finishing status",
    ownershipType: "Ownership Type",
    enterOwnershipType: "Enter ownership type",
    ownerName: "Owner Name",
    enterOwnerName: "Enter owner name",
    ownerPhone: "Owner Phone",
    enterOwnerPhone: "Enter owner phone number",
    viewDescription: "View Description",
    enterViewDescription: "Enter view description",
    companyName: "Company Name",
    enterCompanyName: "Enter company name",
    contactInfo: "Contact Information",
    additionalDetails: "Additional Details",
  },

  // Not Found
  notFound: {
    title: "404",
    message: "Oops! Page not found",
    returnHome: "Return to Home",
  },

  // Settings
  settings: {
    title: "Settings",
    currency: "Currency",
    currencyDescription: "Manage currency settings",
    displayCurrency: "Display Currency",
    currencyToggleDescription: "Toggle between USD and SYP",
    currentRate: "Current Exchange Rate",
    lastUpdated: "Last Updated",
    never: "Never",
    updateRate: "Update Rate",
    appSettings: "App Settings",
    appSettingsDescription: "Manage application settings",
    comingSoon: "Coming Soon...",
    source: "Source",
  },

  // Property Offers
  propertyOffers: {
    title: "Property Offers",
    searchPlaceholder: "Search properties...",
    propertyType: "Property Type",
    status: "Status",
    allTypes: "All Types",
    allStatuses: "All Statuses",
    resetFilters: "Reset Filters",
    errorLoading: "Error loading properties",
    noPropertiesMatch: "No properties match your criteria.",
    propertyDetailsOverview: "Property details overview",
    title_column: "Title",
    location_column: "Location",
    price_column: "Price",
    type_column: "Type",
    status_column: "Status",
    forSale: "For Sale",
    forRent: "For Rent",
  },

  // Customer Requests
  customerRequests: {
    title: "Customer Requests",
    noRequests: "No customer requests found",
    requestsDescription: "Manage and respond to customer inquiries",
    customerName: "Customer Name",
    requestType: "Request Type",
    dateSubmitted: "Date Submitted",
    status: "Status",
    pending: "Pending",
    inProgress: "In Progress",
    completed: "Completed",
    viewRequest: "View Request",
    respondToRequest: "Respond to Request",
  },
};
