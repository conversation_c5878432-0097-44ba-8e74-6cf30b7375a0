
export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      properties: {
        Row: {
          id: string
          title: string
          description: string
          price: number
          location: string
          bedrooms: number
          bathrooms: number
          floor: number | null
          size: number // Changed from 'area' to 'size'
          furnished: 'furnished' | 'semi-furnished' | 'unfurnished'
          type: 'apartment' | 'house' | 'villa' | 'land' | 'commercial' | 'other'
          status: 'for-sale' | 'for-rent'
          images: string[]
          created_at: string
          updated_at: string
          created_by: string // Changed from 'user_id' to 'created_by' to match the database
          // New fields
          distribution?: string
          directions?: string
          finishing_status?: string
          ownership_type?: string
          owner_name?: string
          owner_phone?: string
          view?: string
        }
        Insert: {
          id?: string
          title: string
          description: string
          price: number
          location: string
          bedrooms: number
          bathrooms: number
          floor?: number | null
          size: number // Changed from 'area' to 'size'
          furnished: 'furnished' | 'semi-furnished' | 'unfurnished'
          type: 'apartment' | 'house' | 'villa' | 'land' | 'commercial' | 'other'
          status: 'for-sale' | 'for-rent'
          images?: string[]
          created_at?: string
          updated_at?: string
          created_by: string // Changed from 'user_id' to 'created_by'
        }
        Update: {
          id?: string
          title?: string
          description?: string
          price?: number
          location?: string
          bedrooms?: number
          bathrooms?: number
          floor?: number | null
          size?: number // Changed from 'area' to 'size'
          furnished?: 'furnished' | 'semi-furnished' | 'unfurnished'
          type?: 'apartment' | 'house' | 'villa' | 'land' | 'commercial' | 'other'
          status?: 'for-sale' | 'for-rent'
          images?: string[]
          created_at?: string
          updated_at?: string
          created_by?: string // Changed from 'user_id' to 'created_by'
        }
      }
      notes: {
        Row: {
          id: string
          property_id: string
          text: string
          created_by: string
          created_at: string
        }
        Insert: {
          id?: string
          property_id: string
          text: string
          created_by: string
          created_at?: string
        }
        Update: {
          id?: string
          property_id?: string
          text?: string
          created_by?: string
          created_at?: string
        }
      }
    }
  }
}
